<?php
$thisIsHomePage = '';
include('header.php');
include('navbar.php');
?>

<!-- Swiper CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content text-center fade-in">
            <h1 class="hero-title gradient-text">مرحباً بك في شي بوكس</h1>
            <p class="hero-subtitle">اكتشفي أجمل الفساتين والأزياء العصرية</p>
            <div class="hero-actions">
                <a href="/category/evening-dresses" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-shopping-bag me-2"></i>تسوقي الآن
                </a>
                <a href="#categories" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-eye me-2"></i>استكشفي الأقسام
                </a>
            </div>
        </div>
    </div>
</section>








<div class="container">

<!--<div class="swiper mySwiper mb-4">
  <div class="swiper-wrapper">
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 1"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 2"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 3"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 4"></div>
  </div>
</div>-->




<style>
.mySwiper {
  width: 100%;
  padding-left: 10px;
  margin-bottom: 30px;
}
.mySwiper .swiper-slide {
  height: 200px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .mySwiper .swiper-slide {
    width: 85%;
  }
}

@media (min-width: 769px) {
  .mySwiper .swiper-slide {
    width: auto;
  }
}

.mySwiper .swiper-slide img {
  width: 100%;
  height: auto;
  object-fit: cover;
}



</style>




    <!-- Categories Section -->
    <section id="categories" class="categories-section">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">تسوقي حسب الأقسام</h2>
            <p class="section-subtitle">اكتشفي مجموعتنا المتنوعة من الفساتين والأزياء</p>
        </div>

        <div class="swiper categoriesSwiper">
            <div class="swiper-wrapper">
                <?php
                    for ($i=0; $i <= count($allcategory)-1 ; $i++) {
                        if($allcategory[$i]['showindex'] == 3){
                ?>
                    <div class="swiper-slide">
                        <div class="category-card hover-lift">
                            <a href="<?php echo $Site_URL.'/category/'.$allcategory[$i]['link']; ?>">
                                <div class="category-image">
                                    <?php if (!empty($allcategory[$i]['photo'])){ ?>
                                        <img alt="<?php echo $allcategory[$i]['name'];?>" src="<?php echo $Site_URL.'/'. $allcategory[$i]['photo']; ?>" title="<?php echo $allcategory[$i]['name'];?>" />
                                    <?php } else { ?>
                                        <img alt="<?php echo $allcategory[$i]['name'];?>" src="<?php echo $Site_URL.'/'. GetTableSet('DefaultImage'); ?>" title="<?php echo $allcategory[$i]['name'];?>" />
                                    <?php } ?>
                                    <div class="category-overlay">
                                        <div class="category-content">
                                            <h3 class="category-name"><?php echo $allcategory[$i]['name']; ?></h3>
                                            <span class="category-cta">تسوقي الآن <i class="fas fa-arrow-left ms-2"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                <?php
                        }
                    }
                ?>
            </div>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <?php
        $products = getAllFrom('*','products_title' ,'WHERE status = 1 AND Quantity > 0 ','ORDER BY id DESC LIMIT '.GetTableSet('IndexProductsComp'));
        if (count($products) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<h2 class="product-title">فساتين سهره السعودية</h2>';
            for ($i=0; $i <= count($products)-1 ; $i++) {
                echo GetProduct($products[$i]['id']);
            }
        }

        echo ' <a href="/category/evening-dresses" class="btn-success2">تسوقي جميع الفساتين</a>';

        $tags = getAllFrom('*','tags' ,'','ORDER BY count DESC ,id ASC ');
        if (count($tags) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<h2 class="product-title">الأكثر بحثاً</h2>';
            for ($i=0; $i <= count($tags)-1 ; $i++) {
                echo '<a class="tgs" style="font-size:'.(100+$tags[$i]['count']).'%" href="'.$tags[$i]['link'].'">'.$tags[$i]['name'].'</a>';
            }
            echo '<div class="col-md-12"><br></div>';
        }

        $blogs = getAllFrom('*' , 'blog' , '', 'ORDER BY id DESC LIMIT 20');
        if(count($blogs) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<div class="col-md-12 padding0">';
            for($i=0; $i<= count($blogs)-1; $i++){
                echo Get_The_Posts($blogs[$i]['id']);
            }
            echo '</div>';
        }
    ?>
</div>

<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
    const swiper = new Swiper(".categorySwiper", {
        slidesPerView: 4,
        spaceBetween: 10,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        breakpoints: {
            0: {
                slidesPerView: 4,
                spaceBetween: 10
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 15
            },
            992: {
                slidesPerView: 6,
                spaceBetween: 20
            }
        }
    });
</script>


<script>
const swiperMain = new Swiper(".mySwiper", {
  loop: true,
  spaceBetween: 15,
  breakpoints: {
    // موبايل
    0: {
      slidesPerView: 'auto',
    },
    // كمبيوتر
    768: {
      slidesPerView: 3,
    },
  },
});



</script>
<script>
// Modern Categories Swiper
const categoriesSwiper = new Swiper(".categoriesSwiper", {
    loop: true,
    spaceBetween: 30,
    centeredSlides: false,
    autoplay: {
        delay: 4000,
        disableOnInteraction: false,
    },
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
        dynamicBullets: true,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
    breakpoints: {
        320: {
            slidesPerView: 1,
            spaceBetween: 20
        },
        480: {
            slidesPerView: 2,
            spaceBetween: 20
        },
        768: {
            slidesPerView: 3,
            spaceBetween: 25
        },
        1024: {
            slidesPerView: 4,
            spaceBetween: 30
        },
        1200: {
            slidesPerView: 5,
            spaceBetween: 30
        }
    }
});

// Categories Menu Toggle
function toggleCategoriesMenu() {
    const dropdown = document.getElementById('categoriesDropdown');
    const trigger = document.querySelector('.menu-trigger');

    if (dropdown.style.display === 'block') {
        dropdown.style.display = 'none';
        trigger.classList.remove('active');
    } else {
        dropdown.style.display = 'block';
        trigger.classList.add('active');
    }
}

// Mobile Menu Functions
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('modernMobileMenu');
    mobileMenu.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeMobileMenu() {
    const mobileMenu = document.getElementById('modernMobileMenu');
    mobileMenu.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function toggleMobileCategory(index) {
    const subcategories = document.getElementById('mobileSubcat' + index);
    const toggleIcon = document.querySelector(`[onclick="toggleMobileCategory(${index})"] .toggle-icon`);

    if (subcategories.style.display === 'block') {
        subcategories.style.display = 'none';
        toggleIcon.style.transform = 'rotate(0deg)';
    } else {
        subcategories.style.display = 'block';
        toggleIcon.style.transform = 'rotate(180deg)';
    }
}

// Navbar Scroll Effect
window.addEventListener('scroll', function() {
    const navbar = document.getElementById('navbar');
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});
</script>

<?php
include('footer.php');
ob_end_flush();
?>



