<body>


    
<?php
$category = getAllFrom('*','category' ,'WHERE status = 1 AND parent = 0','ORDER BY orders DESC ,id DESC');
$categoryx = getAllFrom('*','category' ,'WHERE status = 1 AND parent >= 0','ORDER BY orders DESC ,id DESC');
$allcategory = getAllFrom('*','category' ,'WHERE status = 1 ','ORDER BY orders DESC ,id DESC');

$AllCoupon =  getAllFrom('*' , 'coupon' , '', '');
$AllCouponJson = base64_encode(json_encode($AllCoupon));

if(count($AllCoupon) > 0){
    $rand_coupon = $AllCoupon[rand(0 , count($AllCoupon)-1)];
    echo '
    <div class="coupon-nav">
    <i class="fa fa-gift" aria-hidden="true"></i>  '.$rand_coupon['name'].' : <b>'.$rand_coupon['code'].'</b> <i class="fa fa-gift" aria-hidden="true"></i>
    </div>
    ';
}
?> 
<div class="top-nav-bar" id="navbar">
    <div class="container">
        <div class="row align-items-center">
            <!-- Logo Section -->
            <div class="col-lg-2 col-md-3 col-sm-6 col-6">
                <div class="logo">
                    <a href="<?php echo $Site_URL;?>" class="hover-scale">
                        <img class="logo" src="<?php echo $Site_URL.'/'. GetTableSet ('Logo') ;?>" alt="<?php echo $Site_Name ;?>">
                    </a>
                </div>
            </div>

            <!-- Search Section -->
            <div class="col-lg-6 col-md-5 d-none d-md-block">
                <div class="search-container">
                    <form action="<?php echo $Site_URL;?>/search.php" method="GET">
                        <div class="position-relative">
                            <input type="text" name="q" class="search-input" placeholder="ابحثي عن المنتجات..." required>
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User Actions Section -->
            <div class="col-lg-4 col-md-4 col-sm-6 col-6">
                <div class="user-actions d-flex align-items-center justify-content-end gap-3">
  
                    <!-- User Menu -->
                    <div class="user-menu d-none d-lg-flex align-items-center gap-2">
                        <?php
                        if (isset($_SESSION['userData'])){
                            if ($User_Type >= 1){
                                echo'<div class="dropdown">
                                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-user-shield me-2"></i>الإدارة
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="'.$Site_URL.'/admincp/"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="'.$Site_URL.'/logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                                        </ul>
                                    </div>';
                            }else{
                                echo'<div class="dropdown">
                                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-user me-2"></i>حسابي
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="'.$Site_URL.'/account"><i class="fas fa-user-circle me-2"></i>الملف الشخصي</a></li>
                                            <li><a class="dropdown-item" href="'.$Site_URL.'/account"><i class="fas fa-shopping-bag me-2"></i>طلباتي</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="'.$Site_URL.'/logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                                        </ul>
                                    </div>';
                            }
                        }else{
                            echo'<a href="'.$Site_URL.'/login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول
                                </a>
                                <a href="'.$Site_URL.'/register.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>تسجيل
                                </a>';
                        }
                        ?>
                    </div>

                    <!-- Cart Button -->
                    <div class="cart-container position-relative">
                        <a href="<?php echo $Site_URL.'/cart'?>" class="btn btn-outline-success position-relative hover-lift">
                            <i class="fas fa-shopping-cart me-2"></i>
                            <span class="d-none d-sm-inline">السلة</span>
                            <span id="ccrt" class="notification-badge"><?php echo count($_SESSION['UserCart']) ;?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div class="mobile-search d-md-none">
        <div class="container">
            <div class="search-container">
                <form action="<?php echo $Site_URL;?>/search.php" method="GET">
                    <div class="position-relative">
                        <input type="text" name="q" class="search-input" placeholder="ابحثي عن المنتجات..." required>
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Modern Navigation Menu -->
    <div class="modern-nav-menu">
        <div class="container">
            <div class="row">
                <!-- Quick Links -->
                <div class="col-12 d-md-none">
                    <div class="quick-links d-flex gap-3 py-3">
                        <a href="https://shebox.co/category/evening-dresses/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-dress me-2"></i>جميع الفساتين
                        </a>
                        <a href="https://shebox.co/category/%D8%AA%D8%B3%D9%88%D9%82%D9%8A-%D8%AD%D8%B3%D8%A8-%D8%A7%D9%84%D9%84%D9%88%D9%86" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-palette me-2"></i>تسوقي حسب اللون
                        </a>
                    </div>
                </div>

                <!-- Desktop Categories Menu -->
                <div class="col-12 d-none d-md-block">
                    <div class="categories-menu">
                        <div class="menu-trigger <?php if (isset($thisIsHomePage)){echo 'active' ;}?>" onclick="toggleCategoriesMenu()">
                            <i class="fas fa-bars me-2"></i>
                            <span>تسوقي الآن</span>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </div>

                        <div class="categories-dropdown" id="categoriesDropdown">
                            <div class="categories-grid">
                                <?php
                                if (count($category) > 0){
                                    for ($i=0; $i <= count($category)-1 ; $i++) {
                                ?>
                                    <div class="category-item">
                                        <div class="category-header">
                                            <a href="<?php echo $Site_URL.'/category/'.$category[$i]['link'] ;?>" class="category-link">
                                                <div class="category-image">
                                                    <?php if (!empty($category[$i]['photo'])){ ?>
                                                        <img alt="<?php echo $category[$i]['name'];?>" src="<?php echo $Site_URL.'/'. $category[$i]['photo']; ?>" title="<?php echo $category[$i]['name'];?>" />
                                                    <?php }else{ ?>
                                                        <img alt="<?php echo $category[$i]['name'];?>" src="<?php echo $Site_URL.'/'. GetTableSet ('DefaultImage'); ?>" title="<?php echo $category[$i]['name'];?>" />
                                                    <?php } ?>
                                                </div>
                                                <h4><?php echo $category[$i]['name'];?></h4>
                                            </a>
                                        </div>

                                        <?php
                                        $sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
                                        if (count($sub) > 0){
                                        ?>
                                            <div class="subcategories">
                                                <?php for ($x=0; $x <= count($sub)-1 ; $x++) { ?>
                                                    <a href="<?php echo $Site_URL.'/category/'.$sub[$x]['link']; ?>" class="subcategory-link">
                                                        <?php echo $sub[$x]['name']; ?>
                                                    </a>
                                                <?php } ?>
                                            </div>
                                        <?php } ?>
                                    </div>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
                
                <div class="left-menu clearfix">
                  
                       
                </div>
                <div class="clearfix"></div>
        </div>
                    
                    </div>

                     

   
    <!-- Mobile Menu Toggle -->
    <div class="mobile-menu-toggle d-md-none">
        <button class="btn btn-outline-primary" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
    </div>
</div>

<!-- Modern Mobile Menu -->
<div class="modern-mobile-menu" id="modernMobileMenu">
    <div class="mobile-menu-overlay" onclick="closeMobileMenu()"></div>
    <div class="mobile-menu-content">
        <div class="mobile-menu-header">
            <h3><i class="fas fa-shopping-bag me-2"></i>القائمة</h3>
            <button class="close-btn" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mobile-menu-body">
            <!-- User Section -->
            <div class="mobile-user-section">
                <?php if (isset($_SESSION['userData'])){ ?>
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span>مرحباً بك</span>
                    </div>
                    <div class="user-actions">
                        <?php if ($User_Type >= 1){ ?>
                            <a href="<?php echo $Site_URL;?>/admincp/" class="mobile-menu-link">
                                <i class="fas fa-tachometer-alt"></i>لوحة التحكم
                            </a>
                        <?php }else{ ?>
                            <a href="<?php echo $Site_URL;?>/account" class="mobile-menu-link">
                                <i class="fas fa-user"></i>حسابي
                            </a>
                        <?php } ?>
                        <a href="<?php echo $Site_URL;?>/logout.php" class="mobile-menu-link">
                            <i class="fas fa-sign-out-alt"></i>تسجيل الخروج
                        </a>
                    </div>
                <?php }else{ ?>
                    <div class="guest-actions">
                        <a href="<?php echo $Site_URL;?>/login.php" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                        <a href="<?php echo $Site_URL;?>/register.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>إنشاء حساب
                        </a>
                    </div>
                <?php } ?>
            </div>

            <!-- Categories -->
            <div class="mobile-categories">
                <h4><i class="fas fa-list me-2"></i>الأقسام</h4>
                <?php
                for ($i=0; $i <= count($category)-1 ; $i++) {
                    echo '<div class="mobile-category-item">';
                    echo '<div class="category-toggle" onclick="toggleMobileCategory('.$i.')">';
                    echo '<a href="'.$Site_URL.'/category/'.$category[$i]['link'].'">'.$category[$i]['name'].'</a>';

                    $sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
                    if (count($sub) > 0){
                        echo '<i class="fas fa-chevron-down toggle-icon"></i>';
                    }
                    echo '</div>';

                    if (count($sub) > 0){
                        echo '<div class="mobile-subcategories" id="mobileSubcat'.$i.'">';
                        for ($x=0; $x <= count($sub)-1 ; $x++) {
                            echo '<a href="'.$Site_URL.'/category/'.$sub[$x]['link'].'" class="mobile-subcategory-link">'.$sub[$x]['name'].'</a>';
                        }
                        echo '</div>';
                    }
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>

<div id="CartData" class="modal fade" data-backdrop="static" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
          <div class="CartDataClose">
            <h2>سلة المشتريات</h2>
            <a data-dismiss="modal"><i class="fa fa-window-close" aria-hidden="true"></i></a>
          </div>
          <div id="CartDataRes"></div>  
      </div>
    </div>
  </div>
</div>
  
 
  
<div class="min-content">
  
  
  

   